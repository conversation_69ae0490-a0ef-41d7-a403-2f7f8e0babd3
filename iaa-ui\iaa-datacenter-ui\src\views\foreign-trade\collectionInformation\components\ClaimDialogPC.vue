<template>
  <el-dialog
    :model-value="show"
    :title="readOnly ? '认款详情' : '收款认领'"
    :width="dialogWidth"
    :close-on-click-modal="false"
    :draggable="true"
    :style="dialogStyle"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="form"
      label-width="100px"
      :disabled="readOnly"
      v-loading="formLoading"
      :style="formStyle"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="认领日期" prop="claimDate">
            <el-date-picker
              v-model="form.claimDate"
              type="date"
              value-format="YYYY-MM-DD"
              placeholder="选择日期"
              style="width: 100%"
              :disabled="!isDateDisabled"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="业务员" prop="salesmanName">
            <el-input v-model="form.salesmanName" placeholder="默认当前登录人" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="客户名称" prop="customerName">
            <el-input
              v-model="customerDisplay"
              placeholder="输入选择"
              readonly
              @click="!readOnly && openCustomerDialog()"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="客户编码" prop="customerCode">
            <el-input v-model="form.customerCode" readonly />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="币种" prop="currency">
            <el-input v-model="form.currency" readonly />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="总金额" prop="totalAmount">
            <el-input v-model="form.totalAmount" readonly />
          </el-form-item>
        </el-col>

        <!-- 已选择收款信息 -->
        <el-col :span="12">
          <el-form-item label="已选择">
            <el-input
              :model-value="selectList.join(', ')"
              readonly
              @click="!readOnly && openCollectionDialog()"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="备注" prop="remark">
            <el-input v-model="form.remark" />
          </el-form-item>
        </el-col>
      </el-row>
      <!-- 订单明细 -->
      <div>
        <el-divider class="p-2px">订单明细</el-divider>
        <div v-if="!readOnly" class="mt--8px">
          <el-button type="primary" plain @click="addOrder" size="small">+ 添加订单</el-button>
        </div>
        <el-table
          :data="orders"
          border
          :max-height="tableMaxHeight"
          :header-cell-style="{ background: '#f5f7fa', color: '#606266' }"
          style="width: 100%"
        >
          <el-table-column label="订单号" min-width="160">
            <template #default="{ row, $index }">
              <el-input
                v-if="!readOnly"
                v-model="row.orderNo"
                readonly
                @click="openOrderDialog($index)"
              />
              <span v-else>{{ row.orderNo }}</span>
            </template>
          </el-table-column>
          <el-table-column label="订单金额" min-width="100">
            <template #default="{ row }">
              <span>{{ row.orderAmount }}</span>
            </template>
          </el-table-column>
          <el-table-column label="剩余认款余额" min-width="100">
            <template #default="{ row }">
              <span>{{ row.remainingAmount }}</span>
            </template>
          </el-table-column>
          <el-table-column label="收款比例(%)" min-width="90">
            <template #default="{ row }">
              <el-input
                v-if="!readOnly"
                v-model="row.claimRatio"
                type="number"
                :step="1"
                :min="0"
                :max="100"
                style="width: 100%"
                @change="handleRatioChange(row)"
              />
              <span v-else>{{ row.claimRatio }}</span>
            </template>
          </el-table-column>
          <el-table-column label="认款金额" min-width="120">
            <template #default="{ row }">
              <el-input
                v-if="!readOnly"
                v-model="row.amount"
                type="number"
                :step="1"
                :min="0"
                :max="row.orderAmount"
                style="width: 100%"
                @change="handleAmountChange(row)"
              />
              <span v-else>{{ row.amount }}</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="80" v-if="!readOnly">
            <template #default="{ $index }">
              <el-button type="danger" size="small" @click="removeOrder($index)"> 删除 </el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 费用类别 -->
        <el-divider class="p-2px">费用类别</el-divider>
        <div v-if="!readOnly" class="mt--8px">
          <el-button type="primary" plain @click="addExpense" size="small">+ 添加费用</el-button>
        </div>
        <el-table
          :data="expenses"
          border
          :max-height="tableMaxHeight"
          :header-cell-style="{ background: '#f5f7fa', color: '#606266' }"
          style="width: 100%"
        >
          <el-table-column label="费用类别" min-width="80">
            <template #default="{ row }">
              <el-select v-model="row.expenseType" placeholder="请选择费用类别" style="width: 100%">
                <el-option
                  v-for="dict in getIntDictOptions(DICT_TYPE.FINANCIAL_COSTS_TYPE)"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </template>
          </el-table-column>
          <el-table-column label="金额" min-width="80">
            <template #default="{ row }">
              <el-input
                v-if="!readOnly"
                v-model="row.amount"
                :precision="2"
                type="number"
                :min="0"
                :step="1"
                controls-position="right"
                style="width: 100%"
              />
              <span v-else>{{ row.amount }}</span>
            </template>
          </el-table-column>
          <el-table-column label="费用备注" min-width="120">
            <template #default="{ row }">
              <el-input v-model="row.expenseRemark" v-if="!props.readOnly" />
              <span v-else>{{ row.expenseRemark }}</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="80" v-if="!readOnly">
            <template #default="{ $index }">
              <el-button type="danger" size="small" @click="removeExpense($index)">
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 订单未下 -->
        <el-divider class="p-2px">订单未下</el-divider>
        <el-table
          :data="ordersUnsettled"
          border
          :max-height="Math.min(tableMaxHeight, 150)"
          :header-cell-style="{ background: '#f5f7fa', color: '#606266' }"
          style="width: 100%"
        >
          <el-table-column label="金额" min-width="120">
            <template #default="{ row }">
              <el-input
                v-if="!readOnly"
                v-model="row.amount"
                :precision="2"
                type="number"
                :min="0"
                :step="1"
                controls-position="right"
                style="width: 100%"
              />
              <span v-else>{{ row.amount }}</span>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-form>

    <template #footer>
      <div v-if="!readOnly && !props.isEdit">
        <span class="mr-15px">已填写总金额：{{ totalClaimed }}</span>
        <el-button @click="handleTempSave" :loading="tempSaving">暂存</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitting"> 确认提交 </el-button>
      </div>
      <div v-else-if="props.isEdit">
        <span class="mr-15px">已填写总金额：{{ totalClaimed }}</span>
        <el-button type="primary" @click="handleSubmit" :loading="submitting"> 确认修改 </el-button>
      </div>
      <div v-else>
        <el-button type="primary" @click="handleClose">关闭</el-button>
      </div>
    </template>
  </el-dialog>

  <!-- 客户选择对话框 -->
  <el-dialog
    v-model="customerDialogVisible"
    title="选择客户"
    :width="subDialogWidth"
    append-to-body
  >
    <div class="mb-15px">
      <el-input
        v-model="customerKeyword"
        placeholder="输入客户名称检索"
        clearable
        @input="fetchCustomerData"
      >
        <template #append>
          <el-button @click="fetchCustomerData">搜索</el-button>
        </template>
      </el-input>
    </div>
    <el-table
      :data="customerList"
      v-loading="customerLoading"
      height="300"
      highlight-current-row
      @current-change="handleCustomerSelect"
    >
      <el-table-column prop="name" label="客户名称" />
      <el-table-column prop="code" label="客户编码" />
    </el-table>
    <template #footer>
      <el-button @click="customerDialogVisible = false">取消</el-button>
      <el-button type="primary" @click="confirmCustomer">确认</el-button>
    </template>
  </el-dialog>

  <!-- 订单选择对话框 -->
  <el-dialog v-model="orderDialogVisible" title="选择订单" :width="subDialogWidth" append-to-body>
    <div class="mb-15px">
      <el-input
        v-model="orderKeyword"
        placeholder="输入订单号检索"
        clearable
        @input="fetchOrderData"
      >
        <template #append>
          <el-button @click="fetchOrderData">搜索</el-button>
        </template>
      </el-input>
    </div>
    <el-table
      :data="orderList"
      v-loading="orderLoading"
      height="300"
      highlight-current-row
      @current-change="handleOrderSelect"
    >
      <el-table-column prop="DocNo" label="订单号" width="160" />
      <el-table-column prop="currency" label="币种" width="110" />
      <el-table-column prop="salesPrice" label="订单总金额" width="110" />
      <el-table-column prop="shipPrice" label="已出货金额" width="110" />
      <el-table-column prop="claimedAmount" label="已认领金额" width="120" />
      <el-table-column prop="remainingAmount" label="剩余认款余额" width="120" />
    </el-table>
    <template #footer>
      <el-button @click="orderDialogVisible = false">取消</el-button>
      <el-button type="primary" @click="confirmOrder">确认</el-button>
    </template>
  </el-dialog>

  <!-- 收款信息选择对话框 -->
  <el-dialog
    v-model="collectionDialogVisible"
    title="选择收款信息"
    :width="subDialogWidth"
    append-to-body
  >
    <el-table
      ref="collectionTableRef"
      :data="collectionListOptions"
      height="300"
      @selection-change="handleCollectionSelection"
    >
      <el-table-column type="selection" min-width="15" />
      <el-table-column prop="collectionAccount" label="收款账户" min-width="130" />
      <el-table-column prop="collectionAmount" label="收款金额" min-width="50" />
      <el-table-column prop="currency" label="币种" min-width="30" />
      <el-table-column prop="dateStr" label="收款日期" min-width="50" />
    </el-table>
    <template #footer>
      <el-button @click="collectionDialogVisible = false">取消</el-button>
      <el-button type="primary" @click="confirmCollection">确认</el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ClaimApi } from '@/api/foreign-trade/collectionInformation/claim'
import { CollectionDetailApi } from '@/api/foreign-trade/collectionDetails'
import { InformationApi } from '@/api/foreign-trade/collectionInformation/index'
import { useUserStore } from '@/store/modules/user'
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
const message = useMessage() // 消息弹窗
import dayjs from 'dayjs'

const userStore = useUserStore()

const props = defineProps<{
  show: boolean
  ids?: number[]
  prefill?: any
  readOnly?: boolean
  isEdit?: boolean
}>()

const emit = defineEmits(['update:show', 'success'])

const isDateDisabled = ref(false)
// 基本数据
const formRef = ref()
const formLoading = ref(false)
const submitting = ref(false)
const tempSaving = ref(false)

const claimIdRef = ref<number | null>(null)
const currentOrderIndex = ref(-1)
const selectedCollectionRows = ref<any[]>([])

// 响应式弹窗尺寸和样式
const dialogWidth = computed(() => {
  // 根据屏幕宽度动态调整弹窗宽度
  if (typeof window !== 'undefined') {
    const screenWidth = window.innerWidth
    if (screenWidth <= 1366) {
      return '95%' // 小屏幕笔记本
    } else if (screenWidth <= 1600) {
      return '85%' // 中等屏幕
    } else if (screenWidth <= 1920) {
      return '65%' // 大屏幕
    } else {
      return '55%' // 超大屏幕
    }
  }
  return '85%' // 默认值
})

const dialogStyle = computed(() => {
  // 根据屏幕高度动态调整弹窗位置和最大高度
  if (typeof window !== 'undefined') {
    const screenHeight = window.innerHeight
    const marginTop = Math.max(screenHeight * 0.02, 10) // 最小10px边距
    const maxHeight = screenHeight * 0.95 // 最大占用95%屏幕高度

    return {
      marginTop: `${marginTop}px`,
      maxHeight: `${maxHeight}px`
    }
  }
  return {
    marginTop: '2vh',
    maxHeight: '95vh'
  }
})

const formStyle = computed(() => {
  // 根据屏幕高度动态调整表单内容区域高度
  if (typeof window !== 'undefined') {
    const screenHeight = window.innerHeight
    // 预留空间：弹窗标题栏(60px) + 底部按钮区(80px) + 边距(40px)
    const reservedHeight = 180
    const maxFormHeight = Math.max(screenHeight * 0.95 - reservedHeight, 400)

    return {
      maxHeight: `${maxFormHeight}px`,
      overflowY: 'auto' as const
    }
  }
  return {
    maxHeight: '70vh',
    overflowY: 'auto' as const
  }
})

const tableMaxHeight = computed(() => {
  // 表格最大高度，确保在小屏幕上不会过高
  if (typeof window !== 'undefined') {
    const screenHeight = window.innerHeight
    return Math.min(screenHeight * 0.25, 250) // 最大250px或屏幕高度的25%
  }
  return 250
})

const subDialogWidth = computed(() => {
  // 子对话框宽度，根据屏幕大小调整
  if (typeof window !== 'undefined') {
    const screenWidth = window.innerWidth
    if (screenWidth <= 1366) {
      return '90%' // 小屏幕笔记本
    } else if (screenWidth <= 1600) {
      return '70%' // 中等屏幕
    } else {
      return '50%' // 大屏幕
    }
  }
  return '60%' // 默认值
})

// 表单数据
const form = reactive<any>({
  claimDate: dayjs().format('YYYY-MM-DD'),
  type: 1,
  status: 0,
  salesmanName: userStore.user?.nickname || '',
  customerName: '',
  customerCode: '',
  totalAmount: 0.0,
  currency: '',
  currencyCode: '',
  remark: '',
  id: ''
})

// 订单和费用和订单未下数据
const orders = ref<any[]>([])
const expenses = ref<any[]>([])
const ordersUnsettled = ref<any[]>([
  {
    type: 3,
    amount: 0
  }
])

// 选择列表数据
const selectList = ref<string[]>([])
const collectionList = ref<any[]>([])

// 客户相关
const customerDialogVisible = ref(false)
const customerKeyword = ref('')
const customerLoading = ref(false)
const customerList = ref<any[]>([])
const selectedCustomer = ref<any>(null)

// 订单相关
const orderDialogVisible = ref(false)
const orderKeyword = ref('')
const orderLoading = ref(false)
const orderList = ref<any[]>([])
const selectedOrder = ref<any>(null)

// 收款信息相关
const collectionDialogVisible = ref(false)
const collectionListOptions = ref<any[]>([])

// 计算属性
const readOnly = computed(() => props.readOnly || (props.prefill && props.prefill.readOnly))

const customerDisplay = computed(() =>
  form.customerName && form.customerCode ? `${form.customerName}` : ''
)

// 添加计算认款总金额的方法
const claimTotalAmount = computed(() => {
  // 计算订单明细的认款金额总和
  const orderAmountTotal = orders.value.reduce((sum, order) => {
    return sum + Number(order.amount || 0)
  }, 0)

  // 计算费用类别的金额总和
  const expenseAmountTotal = expenses.value.reduce((sum, expense) => {
    return sum + Number(expense.amount || 0)
  }, 0)

  const ordersUnsettledAmount = ordersUnsettled.value.reduce((sum, item) => {
    return sum + Number(item.amount || 0)
  }, 0)

  // 返回总和
  return orderAmountTotal + expenseAmountTotal + ordersUnsettledAmount
})

// 初始化选中列表
const initSelectList = () => {
  selectList.value = collectionList.value.map(
    (item) => `${item.collectionAccount}-${item.collectionAmount}`
  )
}

// 打开客户选择对话框
const openCustomerDialog = async () => {
  if (readOnly.value) return
  customerDialogVisible.value = true
  await fetchCustomerData()
}

// 获取客户数据
const fetchCustomerData = async () => {
  try {
    customerLoading.value = true
    const res: any = await ClaimApi.getCustomer(customerKeyword.value || '')
    customerList.value = res || []
  } catch (err) {
    console.error('获取客户数据失败:', err)
    message.error('获取客户数据失败')
  } finally {
    customerLoading.value = false
  }
}

// 处理客户选择
const handleCustomerSelect = (row: any) => {
  selectedCustomer.value = row
}

// 确认客户选择
const confirmCustomer = () => {
  if (!selectedCustomer.value) {
    message.error('请选择客户')
    return
  }

  form.customerName = selectedCustomer.value.name
  form.customerCode = selectedCustomer.value.code
  customerDialogVisible.value = false
  orders.value = []
}

// 打开订单选择对话框
const openOrderDialog = (index: number) => {
  if (readOnly.value) return
  if (!form.customerCode) {
    message.error('请先选择客户')
    return
  }

  currentOrderIndex.value = index
  orderKeyword.value = ''
  orderDialogVisible.value = true
  fetchOrderData()
}

// 获取订单数据
const fetchOrderData = async () => {
  console.log(form.id)
  try {
    orderLoading.value = true
    const res: any = await ClaimApi.getOrders({
      code: form.customerCode,
      claimId: form.id,
      DocNo: orderKeyword.value
    })
    orderList.value = res || []
  } catch (err) {
    console.error('获取订单数据失败:', err)
    message.error('获取订单数据失败')
  } finally {
    orderLoading.value = false
  }
}

// 处理订单选择
const handleOrderSelect = (row: any) => {
  selectedOrder.value = row
}

// 确认订单选择
const confirmOrder = () => {
  if (!selectedOrder.value || currentOrderIndex.value < 0) {
    message.error('请选择订单')
    return
  }
  // 检查订单币种是否与主页面币种一致
  if (form.currency && selectedOrder.value.currency) {
    if (form.currency !== selectedOrder.value.currency) {
      message.error(
        `所选订单币种为 ${selectedOrder.value.currency}，与当前认领币种 ${form.currency} 不一致，无法选择`
      )
      return
    }
  }

  const row = orders.value[currentOrderIndex.value]
  row.orderNo = selectedOrder.value.DocNo
  row.orderAmount = selectedOrder.value.salesPrice
  row.remainingAmount = selectedOrder.value.remainingAmount
  row.amount = selectedOrder.value.remainingAmount
  // 默认收款比例为100%，使用四舍五入
  row.claimRatio = Math.round((row.amount / row.orderAmount) * 100)
  orderDialogVisible.value = false
}

// 打开收款信息选择对话框
const openCollectionDialog = async () => {
  if (readOnly.value) return
  collectionDialogVisible.value = true

  try {
    const queryParams = {
      pageNo: -1,
      pageSize: 30,
      status: [0],
      isMe: false
    }
    // 判断是否为继续认领的情况
    if (props.prefill) {
      // 如果是继续认领，设置isMe为true
      queryParams.isMe = true
    }
    const res = await InformationApi.getInformationPage(queryParams)
    collectionListOptions.value = res.list || []
    // 在数据加载完成后，恢复之前的选择状态
    nextTick(() => {
      if (collectionList.value.length > 0 && collectionTableRef.value) {
        // 清空当前选择
        collectionTableRef.value.clearSelection()

        // 重新选择已选中的项
        collectionList.value.forEach((selectedItem) => {
          const rowToSelect = collectionListOptions.value.find(
            (option: any) => option.id === selectedItem.id
          )
          if (rowToSelect) {
            collectionTableRef.value.toggleRowSelection(rowToSelect, true)
          }
        })
      }
    })
  } catch (err) {
    console.error('获取收款信息失败:', err)
    message.error('获取收款信息失败')
  }
}

const collectionTableRef = ref()
// 处理收款信息选择
const handleCollectionSelection = (rows: any[]) => {
  // 检查选中行的币种是否一致
  if (rows.length > 0) {
    const currencies = [...new Set(rows.map((row) => row.currency))]
    if (currencies.length > 1) {
      message.alertError('所选收款记录币种不一致，无法一起认领')
      // 清空选择
      nextTick(() => {
        const table = document.querySelector('.el-dialog .el-table') as any
        if (table && table.__vue__) {
          table.__vue__?.clearSelection()
        }
      })
      return
    }
  }
  selectedCollectionRows.value = rows
}

// 添加订单
const addOrder = () => {
  if (readOnly.value) return
  orders.value.push({
    type: 1,
    orderNo: '',
    orderAmount: 0,
    amount: 0,
    claimRatio: 100
  })
}

// 删除订单
const removeOrder = (index: number) => {
  if (readOnly.value) return
  orders.value.splice(index, 1)
}

// 添加费用
const addExpense = () => {
  if (readOnly.value) return
  expenses.value.push({
    type: 2,
    expenseType: '',
    amount: 0
  })
}

// 处理认款金额变化
const handleAmountChange = (row: any) => {
  if (row.amount > row.remainingAmount) {
    row.amount = row.remainingAmount
    // 同步更新收款比例，使用四舍五入
    if (row.orderAmount > 0) {
      row.claimRatio = Math.round((row.amount / row.orderAmount) * 100)
    }
    message.warning('认款金额不能超过剩余可认款金额，收款比例已自动调整')
  } else {
    // 同步更新收款比例，使用四舍五入
    if (row.orderAmount > 0) {
      row.claimRatio = Math.round((row.amount / row.orderAmount) * 100)
    }
  }
}

// 处理收款比例变化
const handleRatioChange = (row: any) => {
  if (row.claimRatio !== undefined && row.claimRatio !== null) {
    // 根据收款比例计算认款金额，使用四舍五入
    const calculatedAmount = Math.round((row.orderAmount * row.claimRatio) / 100)
    row.amount = calculatedAmount

    // 确保认款金额不超过剩余可认款金额
    if (row.amount > row.remainingAmount) {
      row.amount = row.remainingAmount
      // 重新计算收款比例，使用四舍五入
      if (row.orderAmount > 0) {
        row.claimRatio = Math.round((row.amount / row.orderAmount) * 100)
      }
      message.warning('认款金额不能超过剩余可认款金额，收款比例已自动调整')
    }
  }
}

// 删除费用
const removeExpense = (index: number) => {
  if (readOnly.value) return
  expenses.value.splice(index, 1)
}

// 确认收款信息选择
const confirmCollection = () => {
  if (!selectedCollectionRows.value.length) {
    message.error('请选择收款信息')
    return
  }

  // 再次检查币种一致性
  const currencies = [...new Set(selectedCollectionRows.value.map((row) => row.currency))]
  if (currencies.length > 1) {
    message.alertError('所选收款记录币种不一致，无法一起认领')
    return
  }

  collectionList.value = selectedCollectionRows.value.map((item) => ({
    id: item.id,
    dateStr: item.dateStr,
    collectionAccount: item.collectionAccount,
    collectionId: item.collectionId,
    collectionAmount: item.collectionAmount,
    currency: item.currency,
    currencyCode: item.currencyCode
  }))

  if (collectionList.value.length > 0) {
    form.currency = collectionList.value[0].currency
    form.currencyCode = collectionList.value[0].currencyCode
  }

  form.totalAmount = collectionList.value.reduce((acc, cur) => acc + cur.collectionAmount, 0)
  initSelectList()
  collectionDialogVisible.value = false
}
const totalClaimed = ref(0)
watch(
  [orders, expenses, ordersUnsettled],
  () => {
    totalClaimed.value = claimTotalAmount.value
  },
  { deep: true }
)

// 如果传入了 prefill（继续认领或查看详情），按后端返回预填数据回显
//打开弹窗
watch(
  () => props.prefill,
  async (data) => {
    if (!data) return
    claimIdRef.value = data.id || null
    form.claimDate = data.claimDate
    form.salesmanName = data.salesmanName || form.salesmanName
    form.customerName = data.customerName || ''
    form.customerCode = data.customerCode || ''
    form.totalAmount = Number(data.totalAmount || 0)
    form.currency = data.currency || ''
    form.currencyCode = data.currencyCode || ''
    form.remark = data.remark || ''
    form.id = data.id

    // 收款列表
    collectionList.value = (data.collectionList || []).map((it: any) => ({
      id: it.id,
      dateStr: it.dateStr,
      collectionAccount: it.collectionAccount,
      collectionId: it.collectionId,
      collectionAmount: it.collectionAmount,
      currency: it.currency,
      currencyCode: it.currencyCode
    }))

    // 检查收款列表币种是否一致
    if (collectionList.value.length > 0) {
      const currencies = [...new Set(collectionList.value.map((item) => item.currency))]
      if (currencies.length > 1) {
        message.alertError('收款记录币种不一致')
        return
      }
    }

    initSelectList()

    // 明细
    orders.value = []
    expenses.value = []
    ordersUnsettled.value = [
      {
        type: 3,
        amount: 0
      }
    ]
    ;(data.detailList || []).forEach((d: any) => {
      if (d.type === 1) {
        // 计算收款比例，如果已有认款金额和订单金额，使用四舍五入
        let claimRatio = 100
        if (d.orderAmount > 0) {
          claimRatio = Math.round((Number(d.amount || 0) / d.orderAmount) * 100)
        }
        orders.value.push({
          type: 1,
          orderNo: d.orderNo,
          orderAmount: Number(d.orderAmount || 0),
          currency: d.currency || '美元',
          amount: Number(d.amount || 0),
          remainingAmount: Number(d.remainingAmount || 0),
          shipAmount: Number(d.shipAmount || 0),
          claimRatio: claimRatio
        })
      } else if (d.type === 2)
        expenses.value.push({
          type: 2,
          expenseType: d.expenseType,
          currency: d.currency || '美元',
          amount: Number(d.amount || 0),
          remainingAmount: Number(d.remainingAmount || 0),
          shipAmount: Number(d.shipAmount || 0),
          expenseRemark: d.expenseRemark
        })
      else if (d.type === 3) {
        // 处理订单未下数据
        ordersUnsettled.value[0].amount = Number(d.amount || 0)
      }
    })
  },
  { immediate: true }
)

// 构建明细列表
const buildDetailList = () => {
  const detailList = [
    ...orders.value.map((o) => ({
      type: 1,
      orderNo: o.orderNo,
      orderAmount: o.orderAmount,
      amount: Number(o.amount || 0),
      remainingAmount: Number(o.remainingAmount || 0),
      shipAmount: Number(o.shipAmount || 0)
    })),
    ...expenses.value.map((e) => ({
      type: 2,
      expenseType: e.expenseType,
      amount: Number(e.amount || 0),
      remainingAmount: Number(e.remainingAmount || 0),
      expenseRemark: e.expenseRemark
    })),
    ...ordersUnsettled.value
      .map((c) => ({
        type: 3,
        amount: Number(c.amount || 0)
      }))
      .filter((item) => item.amount > 0)
  ]

  return detailList
}

// 暂存
const handleTempSave = async () => {
  if (tempSaving.value) return
  if (!collectionList.value.length) {
    message.error('未选择任何收款记录')
    return
  }

  // if (!orders.value.length) return message.error('未选择订单')
  if (!rulesClaim()) return

  const payload: any = {
    id: claimIdRef.value || undefined,
    claimDate: form.claimDate || dayjs().format('YYYY-MM-DD'),
    type: form.type,
    status: 2, // 暂存状态
    salesmanName: form.salesmanName,
    customerName: form.customerName,
    customerCode: form.customerCode,
    currency: form.currency,
    currencyCode: form.currencyCode,
    totalAmount: form.totalAmount,
    remark: form.remark,
    detailList: buildDetailList(),
    collectionList: collectionList.value
  }

  try {
    tempSaving.value = true
    const res = await ClaimApi.createClaim(payload)
    if (res && res === '操作成功') {
      claimIdRef.value = null
      message.success('暂存成功')
      emit('success')
      handleClose()
    } else {
      message.error(res)
    }
  } catch (err) {
    console.error('暂存失败:', err)
    message.error('暂存失败')
  } finally {
    tempSaving.value = false
  }
}

// 提交
const handleSubmit = async () => {
  if (submitting.value) return
  if (!collectionList.value.length) {
    message.error('未选择任何收款记录')
    return
  }

  // 校验认款总金额是否等于收款总金额
  // 订单金额 + 费用金额 + 订单未下金额 = 总金额
  const orderAmountTotal = orders.value.reduce((sum, order) => {
    return sum + Number(order.amount || 0)
  }, 0)

  const expenseAmountTotal = expenses.value.reduce((sum, expense) => {
    return sum + Number(expense.amount || 0)
  }, 0)

  const ordersUnsettledAmount = ordersUnsettled.value.reduce((sum, item) => {
    return sum + Number(item.amount || 0)
  }, 0)
  const totalClaimAmount = orderAmountTotal + expenseAmountTotal + ordersUnsettledAmount
  const collectionAmount = form.totalAmount
  if (Math.abs(totalClaimAmount - collectionAmount) > 0.01) {
    message.alertError(`认款总金额（${totalClaimAmount}）必须等于收款总金额（${collectionAmount}）`)
    return
  }

  if (!rulesClaim()) return
  const payload: any = {
    id: claimIdRef.value || undefined,
    claimDate: form.claimDate || dayjs().format('YYYY-MM-DD'),
    type: form.type,
    status: form.status,
    salesmanName: form.salesmanName,
    customerName: form.customerName,
    customerCode: form.customerCode,
    currency: form.currency,
    currencyCode: form.currencyCode,
    totalAmount: form.totalAmount,
    remark: form.remark,
    detailList: buildDetailList(),
    collectionList: collectionList.value
  }

  try {
    submitting.value = true
    const res = await ClaimApi.createClaim(payload)
    if (res && res === '操作成功') {
      claimIdRef.value = null
      message.success('认领成功')
      emit('success')
      handleClose()
    } else {
      message.error(res)
    }
  } catch (err) {
    console.error('认领失败:', err)
    message.error('认领失败')
  } finally {
    submitting.value = false
  }
}

// 关闭对话框
const handleClose = () => {
  emit('update:show', false)
  // 重置表单数据
  resetForm()
}

// 重置表单
const resetForm = () => {
  Object.assign(form, {
    claimDate: dayjs().format('YYYY-MM-DD'),
    type: 1,
    status: 0,
    salesmanName: userStore.user?.nickname || '',
    customerName: '',
    customerCode: '',
    totalAmount: 0.0,
    currency: '',
    id: ''
  })

  orders.value = []
  expenses.value = []
  collectionList.value = []
  selectList.value = []
  claimIdRef.value = null
  // 重置订单未下数据，保留默认记录
  ordersUnsettled.value = [
    {
      type: 3,
      amount: 0
    }
  ]
}

// 监听 ids 属性变化
watch(
  () => props.ids,
  async (val) => {
    if (val && val.length && !props.prefill) {
      // 加载收款信息并检查币种一致性
      try {
        const details = await InformationApi.getInformation(val)
        // 检查币种是否一致
        const currencies = [...new Set(details.map((item: any) => item.currency))]
        if (currencies.length > 1) {
          message.alertError('所选收款记录币种不一致，无法一起认领')
          return
        }

        collectionList.value = details.map((it: any) => ({
          id: it.id,
          dateStr: it.dateStr,
          collectionAccount: it.collectionAccount,
          collectionId: it.collectionId,
          collectionAmount: it.collectionAmount,
          currency: it.currency,
          currencyCode: it.currencyCode
        }))

        if (collectionList.value.length > 0) {
          form.currency = collectionList.value[0].currency
          form.currencyCode = collectionList.value[0].currencyCode
        }

        form.totalAmount = collectionList.value.reduce((acc, cur) => acc + cur.collectionAmount, 0)
        initSelectList()
      } catch (err) {
        console.error('加载收款信息失败:', err)
        message.error('加载收款信息失败')
      }
    }
    isDateDisabled.value = await CollectionDetailApi.getDate()
  },
  { immediate: true }
)

const rulesClaim = () => {
  // 验证
  if (!form.customerName || !form.customerCode) {
    message.error('客户名称和客户编码不能为空')
    return false
  }
  // 验证订单明细中的认款金额不能为0
  for (let i = 0; i < orders.value.length; i++) {
    const order = orders.value[i]
    if (!order.orderNo) {
      message.error(`第${i + 1}条订单明细的订单号不能为空`)
      return false
    }
    if (order.amount <= 0) {
      message.error(`第${i + 1}条订单明细的认款金额必须大于0`)
      return false
    }
  }

  // 验证费用明细中的金额不能为0
  for (let i = 0; i < expenses.value.length; i++) {
    const expense = expenses.value[i]
    if (
      expense.expenseType === undefined ||
      expense.expenseType === null ||
      expense.expenseType === ''
    ) {
      message.error(`第${i + 1}条费用明细的费用类别不能为空`)
      return false
    }
    if (expense.amount <= 0) {
      message.error(`第${i + 1}条费用明细的金额必须大于0`)
      return false
    }
  }

  return true
}

// 窗口大小变化处理
let resizeListenerAttached = false

const handleResize = () => {
  // 触发计算属性重新计算
  // Vue的响应式系统会自动处理
}

onMounted(() => {
  // 添加窗口大小变化监听
  if (!resizeListenerAttached) {
    window.addEventListener('resize', handleResize)
    resizeListenerAttached = true
  }
})

onBeforeUnmount(() => {
  // 移除窗口大小变化监听
  if (resizeListenerAttached) {
    window.removeEventListener('resize', handleResize)
    resizeListenerAttached = false
  }
})
</script>

<style lang="css" scoped>
/* 弹窗自适应样式 */
:deep(.el-dialog) {
  margin: 0 auto !important;
  display: flex;
  flex-direction: column;
  max-height: 95vh;
  overflow: hidden;
}

:deep(.el-dialog__header) {
  flex-shrink: 0;
  padding: 15px 20px;
  border-bottom: 1px solid #ebeef5;
}

:deep(.el-dialog__body) {
  flex: 1;
  overflow: hidden;
  padding: 15px 20px;
  display: flex;
  flex-direction: column;
}

:deep(.el-dialog__footer) {
  flex-shrink: 0;
  padding: 15px 20px;
  border-top: 1px solid #ebeef5;
}

/* 表单样式优化 */
:deep(.el-form) {
  flex: 1;
  overflow-y: auto;
  padding-right: 5px;
}

:deep(.el-form-item) {
  margin-bottom: 8px;
}

:deep(.el-row) {
  margin-bottom: 0;
}

/* 表格样式优化 */
:deep(.el-table) {
  font-size: 13px;
}

:deep(.el-table th) {
  padding: 8px 0;
  font-size: 12px;
}

:deep(.el-table td) {
  padding: 6px 0;
}

/* 响应式表格列宽调整 */
@media screen and (max-width: 1366px) {
  :deep(.el-table-column) {
    min-width: 80px !important;
  }

  :deep(.el-form-item__label) {
    font-size: 13px;
  }

  :deep(.el-input__inner) {
    font-size: 13px;
  }
}

/* 输入框样式 */
:deep(input[type='number']::-webkit-outer-spin-button),
:deep(input[type='number']::-webkit-inner-spin-button) {
  -webkit-appearance: none;
  margin: 0;
}

/* 滚动条样式优化 */
:deep(.el-form::-webkit-scrollbar) {
  width: 6px;
}

:deep(.el-form::-webkit-scrollbar-track) {
  background: #f1f1f1;
  border-radius: 3px;
}

:deep(.el-form::-webkit-scrollbar-thumb) {
  background: #c1c1c1;
  border-radius: 3px;
}

:deep(.el-form::-webkit-scrollbar-thumb:hover) {
  background: #a8a8a8;
}

/* 确保表格在小屏幕上的可读性 */
@media screen and (max-width: 1200px) {
  :deep(.el-table) {
    font-size: 12px;
  }

  :deep(.el-button--small) {
    padding: 5px 8px;
    font-size: 12px;
  }
}
</style>
