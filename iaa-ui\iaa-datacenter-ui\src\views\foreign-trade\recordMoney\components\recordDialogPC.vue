<template>
  <el-dialog
    :model-value="show"
    :title="'录款详情'"
    :width="dialogWidth"
    :close-on-click-modal="false"
    :destroy-on-close="true"
    :draggable="true"
    :style="dialogStyle"
    @close="handleClose"
    @dragover="handleDragOver"
    @drop="handleDrop"
    @dragleave="handleDragLeave"
    :class="{ 'drag-over': dragOver }"
  >
    <el-form
      ref="formRef"
      :model="form"
      label-width="100px"
      :disabled="props.readOnly"
      v-loading="formLoading"
      :style="formStyle"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="日期" prop="claimDate">
            <el-date-picker
              v-model="form.claimDate"
              type="date"
              value-format="YYYY-MM-DD"
              placeholder="选择日期"
              style="width: 100%"
              :disabled="!isDateDisabled"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="业务员" prop="salesmanName">
            <el-input v-model="form.salesmanName" placeholder="默认当前登录人" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="客户名称" prop="customerName">
            <el-input
              v-model="customerDisplay"
              placeholder="输入选择"
              readonly
              @click="!props.readOnly && openCustomerDialog()"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="客户编码" prop="customerCode">
            <el-input v-model="form.customerCode" readonly />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="总金额" prop="totalAmount">
            <el-input :value="Number(form.totalAmount).toFixed(3)" readonly />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="币种" prop="currency">
            <el-select
              v-model="form.currency"
              placeholder="请选择币种"
              style="width: 100%"
              @change="handleCurrencyChange"
            >
              <el-option
                v-for="currency in currencyOptions"
                :key="currency.Code"
                :label="currency.Name"
                :value="currency.Code"
              />
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="客户水单" prop="customerList">
            <UploadImgs v-model="form.customerList" height="80px" width="80px" :limit="10" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="收款账户" prop="collectionAccount">
            <el-select
              v-model="form.collectionAccount"
              placeholder="请选择收款账户"
              style="width: 100%"
              :disabled="props.readOnly"
              @change="handleAccountChange"
            >
              <el-option
                v-for="item in accountOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="备注" prop="remark">
            <el-input v-model="form.remark" />
          </el-form-item>
          <!-- 新增：信保账号说明，仅在收款账户为"信保（美金）"时显示 -->
          <el-form-item
            v-if="form.collectionAccount === '信保（美金）'"
            label="说明"
            prop="creditInsurance"
            :rules="[{ required: true, message: '请选择信保账号说明', trigger: 'change' }]"
          >
            <el-select
              v-model="form.creditInsurance"
              placeholder="请选择信保账号说明"
              style="width: 100%"
              :disabled="props.readOnly"
            >
              <el-option label="ScentaChina" value="ScentaChina" />
              <el-option label="ScentMachine" value="ScentMachine" />
              <el-option label="ScentMarketing" value="ScentMarketing" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 订单明细 -->
      <div>
        <el-divider class="p-2px">订单明细</el-divider>
        <div v-if="!props.readOnly" class="mt--8px">
          <el-button type="primary" plain @click="addOrder" size="small">+ 添加订单</el-button>
        </div>
        <el-table
          :data="orders"
          border
          :header-cell-style="{ background: '#f5f7fa', color: '#606266' }"
          :max-height="tableMaxHeight"
          style="width: 100%"
        >
          <el-table-column label="订单号" min-width="120">
            <template #default="{ row, $index }">
              <el-input
                v-if="!props.readOnly"
                v-model="row.orderNo"
                readonly
                @click="openOrderDialog($index)"
              />
              <span v-else>{{ row.orderNo }}</span>
            </template>
          </el-table-column>
          <el-table-column label="订单金额">
            <template #default="{ row }">
              <span>{{ row.orderAmount }}</span>
            </template>
          </el-table-column>
          <el-table-column label="剩余认款余额">
            <template #default="{ row }">
              <span>{{ row.remainingAmount }}</span>
            </template>
          </el-table-column>
          <el-table-column label="收款比例(%)">
            <template #default="{ row }">
              <el-input
                v-if="!readOnly"
                v-model="row.claimRatio"
                type="number"
                :step="1"
                :min="0"
                :max="100"
                style="width: 100%"
                @change="handleRatioChange(row)"
              />
              <span v-else>{{ row.claimRatio }}</span>
            </template>
          </el-table-column>
          <el-table-column label="认款金额">
            <template #default="{ row }">
              <el-input
                v-if="!props.readOnly"
                v-model="row.amount"
                type="number"
                :step="1"
                :min="0"
                :max="row.orderAmount"
                controls-position="right"
                style="width: 100%"
                @change="handleAmountChange(row)"
              />
              <span v-else>{{ row.amount }}</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="80" v-if="!props.readOnly">
            <template #default="{ $index }">
              <el-button type="danger" size="small" @click="removeOrder($index)"> 删除 </el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 费用类别 -->
        <el-divider class="p-2px">费用类别</el-divider>
        <div v-if="!props.readOnly" class="mt--8px">
          <el-button type="primary" plain @click="addExpense" size="small">+ 添加费用</el-button>
        </div>
        <el-table
          :data="expenses"
          border
          :header-cell-style="{ background: '#f5f7fa', color: '#606266' }"
          :max-height="tableMaxHeight"
          style="width: 100%"
        >
          <el-table-column label="费用类别" min-width="80">
            <template #default="{ row }">
              <el-select v-model="row.expenseType" placeholder="请选择费用类别">
                <el-option
                  v-for="dict in getIntDictOptions(DICT_TYPE.FINANCIAL_COSTS_TYPE)"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </template>
          </el-table-column>
          <el-table-column label="金额" min-width="80">
            <template #default="{ row }">
              <el-input
                v-if="!props.readOnly"
                v-model="row.amount"
                :precision="2"
                type="number"
                :min="0"
                :step="1"
                controls-position="right"
                style="width: 100%"
              />
              <span v-else>{{ row.amount }}</span>
            </template>
          </el-table-column>
          <el-table-column label="费用备注" min-width="120">
            <template #default="{ row }">
              <el-input v-model="row.expenseRemark" v-if="!props.readOnly" />
              <span v-else>{{ row.expenseRemark }}</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="80" v-if="!props.readOnly">
            <template #default="{ $index }">
              <el-button type="danger" size="small" @click="removeExpense($index)">
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <!-- 订单未下 -->
        <el-divider class="p-2px">订单未下</el-divider>
        <el-table
          :data="ordersUnsettled"
          border
          :max-height="Math.min(tableMaxHeight, 150)"
          :header-cell-style="{ background: '#f5f7fa', color: '#606266' }"
          style="width: 100%"
        >
          <el-table-column label="金额" min-width="120">
            <template #default="{ row }">
              <el-input
                v-if="!readOnly"
                v-model="row.amount"
                :precision="2"
                type="number"
                :min="0"
                :step="1"
                controls-position="right"
                style="width: 100%"
              />
              <span v-else>{{ row.amount }}</span>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-form>
    <template #footer>
      <div v-if="!props.readOnly && !props.isEdit">
        <span class="mr-15px">已填写总金额：{{ totalClaimed }}</span>
        <el-button @click="handleTempSave" :loading="tempSaving">暂存</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitting">确认提交</el-button>
      </div>
      <div v-else-if="!props.readOnly && props.isEdit">
        <el-button type="primary" @click="handleSubmit" :loading="submitting">确定修改</el-button>
      </div>
      <div v-else>
        <span v-if="form.status === 3" class="mr-5">
          <el-button type="success" v-hasPermi="['record:money:status']" @click="handleClaim"
          >确认收款</el-button
          >
        </span>

        <el-button type="primary" @click="handleClose">关闭</el-button>
      </div>
    </template>
  </el-dialog>

  <!-- 客户选择对话框 -->
  <el-dialog v-model="customerDialogVisible" title="选择客户" :width="subDialogWidth" append-to-body>
    <div class="mb-15px">
      <el-input
        v-model="customerKeyword"
        placeholder="输入客户名称检索"
        clearable
        @input="fetchCustomerData"
      >
        <template #append>
          <el-button @click="fetchCustomerData">搜索</el-button>
        </template>
      </el-input>
    </div>
    <el-table
      :data="customerList"
      height="300"
      v-loading="loading"
      highlight-current-row
      @current-change="handleCustomerSelect"
    >
      <el-table-column prop="name" label="客户名称" />
      <el-table-column prop="code" label="客户编码" />
    </el-table>
    <template #footer>
      <el-button @click="customerDialogVisible = false">取消</el-button>
      <el-button type="primary" @click="confirmCustomer">确认</el-button>
    </template>
  </el-dialog>

  <!-- 订单选择对话框 -->
  <el-dialog v-model="orderDialogVisible" title="选择订单" :width="subDialogWidth" append-to-body>
    <div class="mb-15px">
      <el-input
        v-model="orderKeyword"
        placeholder="输入订单号检索"
        clearable
        @input="fetchOrderData"
      >
        <template #append>
          <el-button @click="fetchOrderData">搜索</el-button>
        </template>
      </el-input>
    </div>
    <el-table
      :data="orderList"
      height="300"
      highlight-current-row
      @current-change="handleOrderSelect"
    >
      <el-table-column prop="DocNo" label="订单号" width="160" />
      <el-table-column prop="currency" label="币种" width="110" />
      <el-table-column prop="salesPrice" label="订单总金额" width="110" />
      <el-table-column prop="shipPrice" label="已出货金额" width="110" />
      <el-table-column prop="claimedAmount" label="已认领金额" width="120" />
      <el-table-column prop="remainingAmount" label="剩余认款余额" width="120" />
    </el-table>
    <template #footer>
      <el-button @click="orderDialogVisible = false">取消</el-button>
      <el-button type="primary" @click="confirmOrder">确认</el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
  import { ClaimApi } from '@/api/foreign-trade/collectionInformation/claim'
  import { CollectionDetailApi } from '@/api/foreign-trade/collectionDetails'
  import { getAccount } from '@/api/foreign-trade/account/index'
  import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
  import { useUserStore } from '@/store/modules/user'
  import { updateFile } from '@/api/infra/file/index'

  const message = useMessage() // 消息弹窗
  import dayjs from 'dayjs'

  const userStore = useUserStore()

  const props = defineProps<{
    show: boolean
    id?: number
    readOnly?: boolean
    isAdd?: boolean
    isEdit?: boolean
    default: false
  }>()

  const emit = defineEmits(['update:show', 'success'])

  const isDateDisabled = ref(false)
  // 基本数据
  const formRef = ref()
  const formLoading = ref(false)
  const submitting = ref(false)
  const tempSaving = ref(false)

  const claimIdRef = ref<number | null>(null)
  const currentOrderIndex = ref(-1)

  // 响应式弹窗尺寸和样式
  const dialogWidth = computed(() => {
    // 根据屏幕宽度动态调整弹窗宽度
    if (typeof window !== 'undefined') {
      const screenWidth = window.innerWidth
      if (screenWidth <= 1366) {
        return '95%' // 小屏幕笔记本
      } else if (screenWidth <= 1600) {
        return '85%' // 中等屏幕
      } else if (screenWidth <= 1920) {
        return '65%' // 大屏幕
      } else {
        return '55%' // 超大屏幕
      }
    }
    return '85%' // 默认值
  })

  const dialogStyle = computed(() => {
    // 根据屏幕高度动态调整弹窗位置和最大高度
    if (typeof window !== 'undefined') {
      const screenHeight = window.innerHeight
      const marginTop = Math.max(screenHeight * 0.02, 10) // 最小10px边距
      const maxHeight = screenHeight * 0.95 // 最大占用95%屏幕高度

      return {
        marginTop: `${marginTop}px`,
        maxHeight: `${maxHeight}px`
      }
    }
    return {
      marginTop: '2vh',
      maxHeight: '95vh'
    }
  })

  const formStyle = computed(() => {
    // 根据屏幕高度动态调整表单内容区域高度
    if (typeof window !== 'undefined') {
      const screenHeight = window.innerHeight
      // 预留空间：弹窗标题栏(60px) + 底部按钮区(80px) + 边距(40px)
      const reservedHeight = 180
      const maxFormHeight = Math.max(screenHeight * 0.95 - reservedHeight, 400)

      return {
        maxHeight: `${maxFormHeight}px`,
        overflowY: 'auto' as const
      }
    }
    return {
      maxHeight: '70vh',
      overflowY: 'auto' as const
    }
  })

  const tableMaxHeight = computed(() => {
    // 表格最大高度，确保在小屏幕上不会过高
    if (typeof window !== 'undefined') {
      const screenHeight = window.innerHeight
      return Math.min(screenHeight * 0.3, 300) // 最大300px或屏幕高度的30%
    }
    return 300
  })

  const subDialogWidth = computed(() => {
    // 子对话框宽度，根据屏幕大小调整
    if (typeof window !== 'undefined') {
      const screenWidth = window.innerWidth
      if (screenWidth <= 1366) {
        return '90%' // 小屏幕笔记本
      } else if (screenWidth <= 1600) {
        return '70%' // 中等屏幕
      } else {
        return '50%' // 大屏幕
      }
    }
    return '60%' // 默认值
  })

  // 表单数据
  const form = reactive<any>({
    claimDate: dayjs().format('YYYY-MM-DD'),
    type: 2,
    status: 0,
    salesmanName: userStore.user?.nickname || '',
    customerName: '',
    customerCode: '',
    totalAmount: 0.0,
    currency: '',
    currencyCode: '',
    customerList: [],
    collectionAccount: '',
    creditInsurance: '',
    collectionId: '',
    remark: ''
  })

  // 订单和费用数据
  const orders = ref<any[]>([])
  const expenses = ref<any[]>([])
  const ordersUnsettled = ref<any[]>([
    {
      type: 3,
      amount: 0
    }
  ])

  // 客户相关
  const customerDialogVisible = ref(false)
  const customerKeyword = ref('')
  const customerList = ref<any[]>([])
  const selectedCustomer = ref<any>(null)

  // 订单相关
  const orderDialogVisible = ref(false)
  const orderKeyword = ref('')
  const orderList = ref<any[]>([])
  const selectedOrder = ref<any>(null)

  const customerDisplay = computed(() =>
    form.customerName && form.customerCode ? `${form.customerName}` : ''
  )

  // 添加计算认款总金额的方法
  const claimTotalAmount = computed(() => {
    // 计算订单明细的认款金额总和
    const orderAmountTotal = orders.value.reduce((sum, order) => {
      return sum + Number(order.amount || 0)
    }, 0)

    // 计算费用类别的金额总和
    const expenseAmountTotal = expenses.value.reduce((sum, expense) => {
      return sum + Number(expense.amount || 0)
    }, 0)

    const ordersUnsettledAmount = ordersUnsettled.value.reduce((sum, item) => {
      return sum + Number(item.amount || 0)
    }, 0)

    // 返回总和
    return orderAmountTotal + expenseAmountTotal + ordersUnsettledAmount
  })

  const totalClaimed = ref(0)
  // 添加一个监听器，当订单或费用金额变化时更新总金额
  watch(
    [orders, expenses, ordersUnsettled],
    () => {
      form.totalAmount = claimTotalAmount.value
      totalClaimed.value = claimTotalAmount.value
    },
    { deep: true }
  )

  // 打开客户选择对话框
  const openCustomerDialog = async () => {
    if (props.readOnly) return
    customerDialogVisible.value = true
    await fetchCustomerData()
  }

  const loading = ref(false)
  // 获取客户数据
  const fetchCustomerData = async () => {
    try {
      loading.value = true
      const res: any = await ClaimApi.getCustomer(customerKeyword.value || '')
      customerList.value = res || []
    } catch (err) {
      console.error('获取客户数据失败:', err)
      message.error('获取客户数据失败')
    } finally {
      loading.value = false
    }
  }

  // 处理客户选择
  const handleCustomerSelect = (row: any) => {
    selectedCustomer.value = row
  }

  // 确认客户选择
  const confirmCustomer = () => {
    if (!selectedCustomer.value) {
      message.error('请选择客户')
      return
    }

    form.customerName = selectedCustomer.value.name
    form.customerCode = selectedCustomer.value.code
    customerDialogVisible.value = false
    orders.value = []
  }

  // 打开订单选择对话框
  const openOrderDialog = (index: number) => {
    if (props.readOnly) return
    if (!form.customerCode) {
      message.error('请先选择客户')
      return
    }

    currentOrderIndex.value = index
    orderKeyword.value = ''
    orderDialogVisible.value = true
    fetchOrderData()
  }

  // 获取订单数据
  const fetchOrderData = async () => {
    try {
      const res: any = await ClaimApi.getOrders({
        code: form.customerCode,
        DocNo: orderKeyword.value
      })
      orderList.value = res || []
    } catch (err) {
      console.error('获取订单数据失败:', err)
      message.error('获取订单数据失败')
    }
  }

  // 处理订单选择
  const handleOrderSelect = (row: any) => {
    // 检查币种是否匹配
    const selectedCurrency = currencyOptions.value.find(
      (currency) => currency.Code === form.currencyCode
    )

    if (selectedCurrency && row) {
      // 获取当前选中币种的名称
      const selectedCurrencyName = selectedCurrency.Name
      // 比较币种名称是否一致
      if (selectedCurrencyName !== row.currency) {
        message.warning(
          `所选订单币种为 ${row.currency}，与当前选择的币种 ${selectedCurrencyName} 不一致，无法选择`
        )
        // 清空当前选择
        selectedOrder.value = null
        return
      }
    }

    selectedOrder.value = row
  }

  // 确认订单选择
  const confirmOrder = () => {
    if (!selectedOrder.value || currentOrderIndex.value < 0) {
      message.error('请选择订单')
      return
    }

    const row = orders.value[currentOrderIndex.value]
    row.orderNo = selectedOrder.value.DocNo
    row.orderAmount = selectedOrder.value.salesPrice
    row.remainingAmount = selectedOrder.value.remainingAmount
    row.amount = selectedOrder.value.remainingAmount
    row.currency = selectedOrder.value.currency
    row.claimRatio = Math.round((row.amount / row.orderAmount) * 100)
    orderDialogVisible.value = false
  }

  // 添加订单
  const addOrder = () => {
    if (props.readOnly) return
    orders.value.push({
      type: 1,
      orderNo: '',
      orderAmount: 0,
      amount: 0,
      remainingAmount: 0,
      shipAmount: 0,
      claimRatio: 100
    })
  }

  // 删除订单
  const removeOrder = (index: number) => {
    if (props.readOnly) return
    orders.value.splice(index, 1)
  }

  // 监听主表币种变化，同步更新费用项币种
  watch(
    () => form.currency,
    (newCurrency) => {
      if (newCurrency) {
        expenses.value.forEach((expense) => {
          expense.currency = newCurrency
        })
      }
    }
  )

  // 添加费用
  const addExpense = () => {
    if (props.readOnly) return
    expenses.value.push({
      type: 2,
      expenseType: '',
      amount: 0,
      currency: form.currency || '美元'
    })
  }

  // 处理收款比例变化
  const handleRatioChange = (row: any) => {
    if (row.claimRatio !== undefined && row.claimRatio !== null) {
      // 根据收款比例计算认款金额，使用四舍五入
      const calculatedAmount = Math.round((row.orderAmount * row.claimRatio) / 100)
      row.amount = calculatedAmount

      // 确保认款金额不超过剩余可认款金额
      if (row.amount > row.remainingAmount) {
        row.amount = row.remainingAmount
        // 重新计算收款比例，使用四舍五入
        if (row.orderAmount > 0) {
          row.claimRatio = Math.round((row.amount / row.orderAmount) * 100)
        }
        message.warning('认款金额不能超过剩余可认款金额，收款比例已自动调整')
      }
    }
  }

  // 处理认款金额变化
  const handleAmountChange = (row: any) => {
    if (row.amount > row.remainingAmount) {
      row.amount = row.remainingAmount
      // 同步更新收款比例，使用四舍五入
      if (row.orderAmount > 0) {
        row.claimRatio = Math.round((row.amount / row.orderAmount) * 100)
      }
      message.warning('认款金额不能超过剩余可认款金额，收款比例已自动调整')
    } else {
      // 同步更新收款比例，使用四舍五入
      if (row.orderAmount > 0) {
        row.claimRatio = Math.round((row.amount / row.orderAmount) * 100)
      }
    }
  }

  // 删除费用
  const removeExpense = (index: number) => {
    if (props.readOnly) return
    expenses.value.splice(index, 1)
  }

  // 构建明细列表
  const buildDetailList = () => [
    ...orders.value.map((o) => ({
      type: 1,
      orderNo: o.orderNo,
      orderAmount: o.orderAmount,
      amount: Number(o.amount || 0),
      remainingAmount: Number(o.remainingAmount || 0),
      shipAmount: Number(o.shipAmount || 0)
    })),
    ...expenses.value.map((e) => ({
      type: 2,
      expenseType: e.expenseType,
      amount: Number(e.amount || 0),
      remainingAmount: 0,
      expenseRemark: e.expenseRemark
    })),
    ...ordersUnsettled.value
      .map((c) => ({
        type: 3,
        amount: Number(c.amount || 0)
      }))
      .filter((item) => item.amount > 0)
  ]

  // 暂存
  const handleTempSave = async () => {
    if (tempSaving.value) return
    const payload: any = {
      id: claimIdRef.value || undefined,
      claimDate: form.claimDate || dayjs().format('YYYY-MM-DD'),
      type: form.type,
      status: 2, // 暂存状态
      salesmanName: form.salesmanName,
      customerName: form.customerName,
      customerCode: form.customerCode,
      currency: form.currency,
      currencyCode: form.currencyCode,
      customerList: form.customerList,
      totalAmount: form.totalAmount,
      collectionAccount: form.collectionAccount,
      creditInsurance: form.creditInsurance,
      collectionId: form.collectionId,
      remark: form.remark,
      detailList: buildDetailList()
    }
    // 表单验证
    if (!(await validateForm())) {
      return
    }
    try {
      tempSaving.value = true
      await ClaimApi.createClaim(payload)
      claimIdRef.value = null
      message.success('暂存成功')
      emit('success')
    } catch (err) {
      console.error('暂存失败:', err)
      message.error('暂存失败')
    } finally {
      tempSaving.value = false
    }
  }

  // 提交
  const handleSubmit = async () => {
    if (submitting.value) return

    // 表单验证
    if (!(await validateForm())) {
      return
    }
    let status = 3
    if (props.isEdit) {
      status = 1
    }
    const payload: any = {
      id: claimIdRef.value || undefined,
      claimDate: form.claimDate || dayjs().format('YYYY-MM-DD'),
      type: form.type,
      status: status, //业务员确认后无法修改
      salesmanName: form.salesmanName,
      customerName: form.customerName,
      customerCode: form.customerCode,
      currency: form.currency,
      currencyCode: form.currencyCode,
      totalAmount: form.totalAmount,
      customerList: form.customerList,
      collectionAccount: form.collectionAccount,
      creditInsurance: form.creditInsurance,
      collectionId: form.collectionId,
      remark: form.remark,
      detailList: buildDetailList()
    }

    try {
      submitting.value = true
      await ClaimApi.createClaim(payload)
      claimIdRef.value = null
      message.success('录款成功')
      emit('success')
    } catch (err) {
      console.error('录款失败:', err)
      message.error('录款失败')
    } finally {
      submitting.value = false
    }
  }

  // 表单验证方法
  const validateForm = async (): Promise<boolean> => {
    // 验证必填字段
    if (!form.claimDate) {
      message.error('日期不能为空')
      return false
    }

    if (!form.salesmanName) {
      message.error('业务员不能为空')
      return false
    }

    if (!form.customerName || !form.customerCode) {
      message.error('客户名称和客户编码不能为空')
      return false
    }

    if (!form.currency) {
      message.error('币种不能为空')
      return false
    }

    if (!props.isEdit) {
      if (!form.collectionAccount) {
        message.error('收款账户不能为空')
        return false
      }
    }

    if (form.collectionAccount === '信保（美金）') {
      if (!form.creditInsurance) {
        message.error('请选择信保账号说明')
        return false
      }
    }

    // 验证总金额
    if (form.totalAmount <= 0) {
      message.error('总金额必须大于0')
      return false
    }

    // 验证订单明细中的认款金额
    for (let i = 0; i < orders.value.length; i++) {
      const order = orders.value[i]
      if (order.amount <= 0) {
        message.error(`第${i + 1}条订单明细的认款金额必须大于0`)
        return false
      }
    }

    // 验证费用明细中的金额（如果有费用明细）
    for (let i = 0; i < expenses.value.length; i++) {
      const expense = expenses.value[i]
      if (expense.amount <= 0) {
        message.error(`第${i + 1}条费用明细的金额必须大于0`)
        return false
      }
    }

    return true
  }

  // 关闭对话框
  const handleClose = () => {
    emit('update:show', false)
    resetForm()
    // // 延迟重置表单，确保动画完成
    // setTimeout(() => {
    //   resetForm()
    //   emit('success')
    // }, 300)
  }

  //确认收款
  const handleClaim = async () => {
    await message.confirm('是否确认收款？')
    await ClaimApi.updateClaim([props.id])
    message.success('确认收款成功')
    emit('success')
  }

  // 重置表单
  const resetForm = () => {
    Object.assign(form, {
      claimDate: dayjs().format('YYYY-MM-DD'),
      type: 2,
      status: 0,
      salesmanName: userStore.user?.nickname || '',
      customerName: '',
      customerCode: '',
      totalAmount: 0.0,
      currency: '',
      collectionAccount: '',
      customerList: [],
      collectionId: '',
      creditInsurance: ''
    })

    orders.value = []
    expenses.value = []
    claimIdRef.value = null
  }

  // 账户相关
  const accountList = ref<any[]>([])
  const accountOptions = computed(() => {
    return accountList.value.map((item) => ({
      label: item.accountName,
      value: item.accountName,
      accountId: item.accountId
    }))
  })

  //币种
  const currencyOptions = ref<any[]>([])

  // 获取账户列表
  const fetchAccountList = async () => {
    try {
      const res: any = await getAccount()
      accountList.value = res || []
    } catch (err) {
      console.error('获取账户列表失败:', err)
      message.error('获取账户列表失败')
    }
  }

  // 获取币种列表
  const fetchCurrencyList = async () => {
    try {
      const res = await ClaimApi.getCurrency()
      currencyOptions.value = res || []
    } catch (err) {
      console.error('获取币种列表失败:', err)
      message.error('获取币种列表失败')
    }
  }

  // 处理账户选择变化
  const handleAccountChange = (value: string) => {
    const selectedAccount = accountList.value.find((item) => item.accountName === value)
    if (selectedAccount) {
      form.collectionId = selectedAccount.accountId
    } else {
      form.collectionId = ''
    }
  }

  // 处理币种选择变化
  const handleCurrencyChange = (value: string) => {
    const selectedCurrency = currencyOptions.value.find((currency) => currency.Code === value)
    if (selectedCurrency) {
      form.currency = selectedCurrency.Name // 币种名称
      form.currencyCode = selectedCurrency.Code // 币种编码
    } else {
      form.currencyCode = ''
    }
  }

  // 获取认款详情
  const getClaimDetail = async (id: number) => {
    if (!id) return

    try {
      formLoading.value = true
      const res: any = await ClaimApi.getClaimDetail(id)

      if (res) {
        // 填充表单基础信息
        claimIdRef.value = res.id
        form.claimDate = res.claimDate
        form.type = res.type
        form.status = res.status
        form.salesmanName = res.salesmanName
        form.customerName = res.customerName
        form.customerCode = res.customerCode
        form.totalAmount = res.totalAmount
        form.currency = res.currency // 币种名称
        form.currencyCode = res.currencyCode // 币种编码
        form.customerList = res.customerList
        form.collectionAccount = res.collectionAccount
        form.creditInsurance = res.creditInsurance
        form.collectionId = res.collectionId
        form.remark = res.remark

        // 处理明细列表
        if (res.detailList && Array.isArray(res.detailList)) {
          // 分离订单和费用
          orders.value = res.detailList
            .filter((item: any) => item.type === 1)
            .map((item: any) => {
              // 计算收款比例，使用四舍五入
              let claimRatio = 100
              if (item.orderAmount > 0) {
                claimRatio = Math.round((Number(item.amount || 0) / item.orderAmount) * 100)
              }

              return {
                type: 1,
                orderNo: item.orderNo || '',
                orderAmount: item.orderAmount || 0,
                amount: item.amount || 0,
                remainingAmount: item.remainingAmount || 0,
                shipAmount: item.shipAmount || 0,
                claimRatio: claimRatio // 添加收款比例字段
              }
            })

          expenses.value = res.detailList
            .filter((item: any) => item.type === 2)
            .map((item: any) => ({
              type: 2,
              expenseType: item.expenseType || '',
              amount: item.amount || 0,
              expenseRemark: item.expenseRemark || ''
            }))
          // 分离未结算订单
          const unsettledData = res.detailList
            .filter((item: any) => item.type === 3)
            .map((item: any) => ({
              type: 3,
              amount: item.amount || 0
            }))
          // 确保 ordersUnsettled 至少有一条数据
          if (unsettledData.length > 0) {
            ordersUnsettled.value = unsettledData
          } else {
            ordersUnsettled.value = [
              {
                type: 3,
                amount: 0
              }
            ]
          }
        } else {
          // 如果没有明细数据，确保 ordersUnsettled 至少有一条数据
          ordersUnsettled.value = [
            {
              type: 3,
              amount: 0
            }
          ]
        }
      }
    } catch (err) {
      console.error('获取认款详情失败:', err)
      message.error('获取认款详情失败')
    } finally {
      formLoading.value = false
    }
  }

  // 添加拖拽相关的响应式变量
  const dragOver = ref(false)

  /**
   * 处理拖拽事件
   * @param event 拖拽事件对象
   */
  const handleDrop = async (event: DragEvent) => {
    // 只在对话框显示时处理拖拽事件
    if (!props.show) return

    event.preventDefault()
    dragOver.value = false

    const files = event.dataTransfer?.files
    if (!files || files.length === 0) return

    try {
      // 过滤出图片文件
      const imageFiles: File[] = []
      for (let i = 0; i < files.length; i++) {
        const file = files[i]
        if (file.type.startsWith('image/')) {
          imageFiles.push(file)
        }
      }

      // 上传所有图片文件
      if (imageFiles.length > 0) {
        const uploadPromises = imageFiles.map((file) => uploadPastedImage(file))
        await Promise.all(uploadPromises)
        message.success(`成功上传${imageFiles.length}张图片`)
      }
    } catch (error) {
      console.error('拖拽上传出错:', error)
      message.error('拖拽上传失败')
    }
  }

  /**
   * 处理拖拽进入事件
   * @param event 拖拽事件对象
   */
  const handleDragOver = (event: DragEvent) => {
    if (!props.show) return

    event.preventDefault()
    dragOver.value = true
  }

  /**
   * 处理拖拽离开事件
   * @param event 拖拽事件对象
   */
  const handleDragLeave = (event: DragEvent) => {
    if (!props.show) return

    // 检查是否真的离开了目标区域
    if (!event.currentTarget || !event.relatedTarget) {
      dragOver.value = false
      return
    }

    // @ts-ignore
    if (!event.currentTarget.contains(event.relatedTarget)) {
      dragOver.value = false
    }
  }

  const isProcessingPaste = ref(false)

  /**
   * 处理粘贴事件
   * @param event 粘贴事件对象
   */
  const handlePaste = async (event: ClipboardEvent) => {
    // 只在对话框显示时处理粘贴事件
    if (!props.show) return

    const items = event.clipboardData?.items
    if (!items) return

    // 检查是否包含图片数据
    let hasImage = false
    for (let i = 0; i < items.length; i++) {
      if (items[i].type.indexOf('image') !== -1) {
        hasImage = true
        break
      }
    }

    // 如果没有图片数据，直接返回
    if (!hasImage) return

    // 设置处理标志，防止重复处理
    if (isProcessingPaste.value) return
    isProcessingPaste.value = true

    try {
      event.preventDefault() // 阻止默认粘贴行为

      // 收集所有图片文件
      const imageFiles: File[] = []
      for (let i = 0; i < items.length; i++) {
        const item = items[i]
        // 检查是否为图片类型
        if (item.type.indexOf('image') !== -1) {
          const file = item.getAsFile()
          if (file) {
            imageFiles.push(file)
          }
        }
      }

      // 并行上传所有图片
      if (imageFiles.length > 0) {
        const uploadPromises = imageFiles.map((file) => uploadPastedImage(file))
        await Promise.all(uploadPromises)
        message.success(`成功上传${imageFiles.length}张图片`)
      }
    } catch (error) {
      console.error('粘贴上传出错:', error)
      message.error('粘贴上传失败')
    } finally {
      // 处理完成后重置标志
      isProcessingPaste.value = false
    }
  }

  const uploadPastedImage = async (file: File) => {
    const res: any = await updateFile({ file: file })
    if (res) {
      form.customerList = [...form.customerList, res.data.url]
    }
  }

  // 确保事件监听器只绑定一次
  let pasteListenerAttached = false
  let resizeListenerAttached = false

  // 窗口大小变化处理函数
  const handleResize = () => {
    // 触发计算属性重新计算
    // Vue的响应式系统会自动处理
  }

  onMounted(() => {
    // 添加全局粘贴事件监听
    if (!pasteListenerAttached) {
      window.addEventListener('paste', handlePaste)
      pasteListenerAttached = true
    }

    // 添加窗口大小变化监听
    if (!resizeListenerAttached) {
      window.addEventListener('resize', handleResize)
      resizeListenerAttached = true
    }
  })

  onBeforeUnmount(() => {
    // 移除粘贴事件监听
    window.removeEventListener('paste', handlePaste)
    pasteListenerAttached = false

    // 移除窗口大小变化监听
    if (resizeListenerAttached) {
      window.removeEventListener('resize', handleResize)
      resizeListenerAttached = false
    }
  })

  // 监听 id 变化，如果存在则获取详情数据
  watch(
    () => props.show,
    async (newShow) => {
      if (newShow) {
        // 延迟执行确保组件完全渲染
        setTimeout(async () => {
          try {
            // 在打开弹窗时预加载币种和账户数据
            await Promise.all([fetchCurrencyList(), fetchAccountList()])

            console.log(props.id + ',' + props.isAdd)
            if (props.id && !props.isAdd) {
              await getClaimDetail(props.id)
            } else if (props.isAdd) {
              resetForm()
            }
          } catch (error) {
            console.error('初始化数据失败:', error)
            message.error('初始化数据失败')
          }
        }, 50)
      }
      isDateDisabled.value = await CollectionDetailApi.getDate()
    },
    { immediate: true }
  )
</script>

<style lang="css" scoped>
  /* 弹窗自适应样式 */
  :deep(.el-dialog) {
    margin: 0 auto !important;
    display: flex;
    flex-direction: column;
    max-height: 95vh;
    overflow: hidden;
  }

  :deep(.el-dialog__header) {
    flex-shrink: 0;
    padding: 15px 20px;
    border-bottom: 1px solid #ebeef5;
  }

  :deep(.el-dialog__body) {
    flex: 1;
    overflow: hidden;
    padding: 15px 20px;
    display: flex;
    flex-direction: column;
  }

  :deep(.el-dialog__footer) {
    flex-shrink: 0;
    padding: 15px 20px;
    border-top: 1px solid #ebeef5;
  }

  /* 表单样式优化 */
  :deep(.el-form) {
    flex: 1;
    overflow-y: auto;
    padding-right: 5px;
  }

  :deep(.el-form-item) {
    margin-bottom: 8px;
  }

  :deep(.el-row) {
    margin-bottom: 0;
  }

  /* 表格样式优化 */
  :deep(.el-table) {
    font-size: 13px;
  }

  :deep(.el-table th) {
    padding: 8px 0;
    font-size: 12px;
  }

  :deep(.el-table td) {
    padding: 6px 0;
  }

  /* 响应式表格列宽调整 */
  @media screen and (max-width: 1366px) {
    :deep(.el-table-column) {
      min-width: 80px !important;
    }

    :deep(.el-form-item__label) {
      font-size: 13px;
    }

    :deep(.el-input__inner) {
      font-size: 13px;
    }
  }

  /* 输入框样式 */
  :deep(input[type='number']::-webkit-outer-spin-button),
  :deep(input[type='number']::-webkit-inner-spin-button) {
    -webkit-appearance: none;
    margin: 0;
  }

  /* 拖拽样式 */
  :deep(.drag-over) {
    outline: 2px dashed #409eff;
    outline-offset: -2px;
  }

  .drag-over-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(64, 158, 255, 0.1);
    z-index: 9999;
    pointer-events: none;
  }

  .drag-over-text {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 24px;
    color: #409eff;
    font-weight: bold;
  }

  /* 滚动条样式优化 */
  :deep(.el-form::-webkit-scrollbar) {
    width: 6px;
  }

  :deep(.el-form::-webkit-scrollbar-track) {
    background: #f1f1f1;
    border-radius: 3px;
  }

  :deep(.el-form::-webkit-scrollbar-thumb) {
    background: #c1c1c1;
    border-radius: 3px;
  }

  :deep(.el-form::-webkit-scrollbar-thumb:hover) {
    background: #a8a8a8;
  }

  /* 确保表格在小屏幕上的可读性 */
  @media screen and (max-width: 1200px) {
    :deep(.el-table) {
      font-size: 12px;
    }

    :deep(.el-button--small) {
      padding: 5px 8px;
      font-size: 12px;
    }
  }
</style>
