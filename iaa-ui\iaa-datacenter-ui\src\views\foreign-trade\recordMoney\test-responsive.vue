<template>
  <div class="test-responsive">
    <h2>弹窗自适应测试页面</h2>
    <div class="screen-info">
      <p>当前屏幕尺寸: {{ screenWidth }} x {{ screenHeight }}</p>
      <p>弹窗宽度: {{ dialogWidth }}</p>
      <p>子对话框宽度: {{ subDialogWidth }}</p>
    </div>
    
    <div class="test-buttons">
      <el-button type="primary" @click="openDialog">打开录款弹窗</el-button>
      <el-button @click="simulateResize(1366, 768)">模拟小屏幕 (1366x768)</el-button>
      <el-button @click="simulateResize(1920, 1080)">模拟大屏幕 (1920x1080)</el-button>
      <el-button @click="simulateResize(2560, 1440)">模拟超大屏幕 (2560x1440)</el-button>
    </div>

    <!-- 录款弹窗组件 -->
    <recordDialogPC
      v-model:show="dialogVisible"
      :id="undefined"
      :isAdd="true"
      @success="handleSuccess"
    />
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted, onBeforeUnmount } from 'vue'
import recordDialogPC from './components/recordDialogPC.vue'

const dialogVisible = ref(false)
const screenWidth = ref(window.innerWidth)
const screenHeight = ref(window.innerHeight)

// 计算弹窗宽度（与组件中的逻辑保持一致）
const dialogWidth = computed(() => {
  if (screenWidth.value <= 1366) {
    return '95%'
  } else if (screenWidth.value <= 1600) {
    return '85%'
  } else if (screenWidth.value <= 1920) {
    return '75%'
  } else {
    return '65%'
  }
})

// 计算子对话框宽度
const subDialogWidth = computed(() => {
  if (screenWidth.value <= 1366) {
    return '90%'
  } else if (screenWidth.value <= 1600) {
    return '70%'
  } else {
    return '50%'
  }
})

const openDialog = () => {
  dialogVisible.value = true
}

const handleSuccess = () => {
  dialogVisible.value = false
}

const simulateResize = (width: number, height: number) => {
  screenWidth.value = width
  screenHeight.value = height
  // 触发窗口resize事件来更新组件
  window.dispatchEvent(new Event('resize'))
}

const updateScreenSize = () => {
  screenWidth.value = window.innerWidth
  screenHeight.value = window.innerHeight
}

onMounted(() => {
  window.addEventListener('resize', updateScreenSize)
})

onBeforeUnmount(() => {
  window.removeEventListener('resize', updateScreenSize)
})
</script>

<style scoped>
.test-responsive {
  padding: 20px;
}

.screen-info {
  background: #f5f7fa;
  padding: 15px;
  border-radius: 4px;
  margin: 20px 0;
}

.screen-info p {
  margin: 5px 0;
  font-family: monospace;
}

.test-buttons {
  margin: 20px 0;
}

.test-buttons .el-button {
  margin-right: 10px;
  margin-bottom: 10px;
}
</style>
